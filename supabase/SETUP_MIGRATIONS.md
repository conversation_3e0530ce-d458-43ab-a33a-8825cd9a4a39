# 数据库迁移设置指南

## 设置步骤

1. **复制示例文件**
   ```bash
   cp supabase/migrations.example/001_user_profiles.sql.example supabase/migrations/001_user_profiles.sql
   ```

2. **编辑迁移文件**
   打开 `supabase/migrations/001_user_profiles.sql`，将以下内容替换为你的管理员邮箱：
   - 将 `<EMAIL>` 替换为你的实际管理员邮箱

3. **运行迁移**
   在 Supabase SQL Editor 中执行修改后的 SQL 文件

## 注意事项

- `/supabase/migrations/` 文件夹已被添加到 `.gitignore`，不会被提交到版本控制
- 示例文件可以安全地提交，因为它们不包含敏感信息
- 每个开发者需要根据自己的需求修改管理员邮箱

## 为现有用户设置管理员权限

如果你已经有用户账号，可以运行以下 SQL 来设置管理员权限：

```sql
UPDATE public.user_profiles 
SET role = 'admin', status = 'approved'
WHERE email = '<EMAIL>';
```