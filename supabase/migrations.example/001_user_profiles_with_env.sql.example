-- This version uses environment variables for admin email
-- Before running, set the admin email in Supabase Dashboard:
-- Settings > Secrets > Add new secret
-- Name: ADMIN_EMAIL
-- Value: <EMAIL>

-- Create user_profiles table to extend auth.users
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES auth.users(id)
);

-- ... (rest of the schema remains the same)

-- Function to create user profile on signup (using environment variable)
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    admin_email TEXT;
BEGIN
    -- Get admin email from vault secrets
    SELECT decrypted_secret INTO admin_email
    FROM vault.decrypted_secrets
    WHERE name = 'ADMIN_EMAIL';
    
    INSERT INTO public.user_profiles (id, email, role, status)
    VALUES (
        NEW.id,
        NEW.email,
        CASE 
            WHEN NEW.email = admin_email THEN 'admin'
            ELSE 'user'
        END,
        CASE 
            WHEN NEW.email = admin_email THEN 'approved'
            WHEN (SELECT registration_mode FROM public.app_settings LIMIT 1) = 'open' THEN 'approved'
            ELSE 'pending'
        END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;