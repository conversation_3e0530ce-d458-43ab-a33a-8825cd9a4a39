# Supabase 设置指南

## 重要：管理员账号
默认管理员账号：`<EMAIL>`
- 使用此邮箱注册的账号将自动成为管理员
- 管理员账号自动批准，无需等待审核

## 1. 创建 Supabase 项目

1. 访问 [https://supabase.com](https://supabase.com)
2. 创建新账号或登录
3. 点击 "New Project" 创建新项目
4. 填写项目信息：
   - 项目名称
   - 数据库密码（请保存好）
   - 选择地区（建议选择距离较近的）

## 2. 获取项目凭据

1. 进入项目仪表板
2. 点击左侧 "Settings" (设置)
3. 选择 "API" 选项卡
4. 复制以下信息：
   - **Project URL**: 这是你的 `NEXT_PUBLIC_SUPABASE_URL`
   - **anon public**: 这是你的 `NEXT_PUBLIC_SUPABASE_ANON_KEY`

## 3. 配置环境变量

1. 复制 `.env.local.example` 为 `.env.local`
2. 填入你的 Supabase 凭据：

```env
NEXT_PUBLIC_SUPABASE_URL=你的项目URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=你的anon密钥
AIHUBMIX_API_KEY=你的AiHubMix API密钥
```

## 4. 配置认证设置

1. 在 Supabase 仪表板中，点击 "Authentication"
2. 点击 "Providers"
3. 确保 "Email" 认证已启用
4. 可选：配置其他登录方式（Google、GitHub等）

## 5. 配置邮件模板（可选）

1. 在 "Authentication" → "Email Templates"
2. 自定义注册确认邮件模板
3. 自定义密码重置邮件模板

## 6. 安全设置

1. 在 "Authentication" → "Policies"
2. 可以添加额外的安全策略
3. 在 "Settings" → "API" → "Authorized domains" 添加你的域名

## 7. 运行数据库迁移

在 Supabase 仪表板中：
1. 点击 "SQL Editor"
2. 点击 "New query"
3. 复制 `supabase/migrations/001_user_profiles.sql` 的内容
4. 粘贴并运行 SQL

## 8. 运行项目

```bash
npm run dev
```

访问 http://localhost:3000，你应该会被重定向到登录页面。

## 9. 管理员功能

1. 使用 `<EMAIL>` 邮箱注册管理员账号
2. 管理员可以：
   - 访问 `/admin` 管理控制台
   - 批准/拒绝用户注册
   - 更改用户角色
   - 切换注册模式（开放注册/需要审批）

## 注意事项

- 首次注册后，需要检查邮箱进行验证
- 确保环境变量正确配置
- 生产环境部署时，记得在 Vercel/其他平台配置相同的环境变量