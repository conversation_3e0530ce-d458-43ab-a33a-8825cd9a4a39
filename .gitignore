# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# Claude Code files
.claude/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# OS generated files
Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Test files
test-api.js

# Logs
logs
*.log

# Supabase migrations (contains admin email)
/supabase/migrations/
# Or if you want to keep migrations but not specific ones:
# /supabase/migrations/001_user_profiles.sql
# /supabase/migrations/002_update_admin_email.sql
# /supabase/migrations/003_fix_admin_rls_policies.sql

# Package lock files (if you want to ignore them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
