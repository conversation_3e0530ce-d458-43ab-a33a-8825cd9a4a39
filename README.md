# Veo 3.0 视频生成器

使用 Google Veo 3.0 模型生成高质量视频的 Web 应用（逆向接口版本）。

## 功能特点

- 🎬 使用文字描述生成视频
- 🎨 现代化的深色界面设计
- 📱 响应式布局，支持各种设备
- 💾 支持视频下载
- 🔄 实时生成状态反馈
- 📂 视频历史记录展示

## 开始使用

### 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. 克隆项目
```bash
git clone <your-repo-url>
cd veo3
```

2. 安装依赖
```bash
npm install
```

3. 配置 API 密钥

编辑 `.env.local` 文件，添加你的 AiHubMix API 密钥：
```
AIHUBMIX_API_KEY=sk-your-api-key-here
```

4. 启动开发服务器
```bash
npm run dev
```

5. 在浏览器中访问 `http://localhost:3000`

## 使用说明

1. 在文本框中输入视频描述（仅支持英文）
2. 点击"生成视频"按钮
3. 等待视频生成完成（通常需要1-2分钟）
4. 生成的视频会显示在下方的视频库中
5. 可以在线播放或下载视频

## API 说明

本应用使用 AiHubMix 提供的 Veo 3.0 逆向接口，通过 OpenAI 兼容方式调用：
- 模型 ID: `veo-3`
- 基础 URL: `https://aihubmix.com/v1`

## 费用说明

- 单次生成费用：$0.41
- 相比官方接口更优惠
- 需要 AiHubMix 账户余额

## 技术栈

- Next.js 14 (App Router)
- TypeScript
- Tailwind CSS
- OpenAI SDK (用于调用 AiHubMix 逆向接口)

## 注意事项

- 使用逆向接口，不保证稳定性
- 建议用于开发环境测试或个人体验
- 生成的视频请及时下载保存
- 目前仅支持英文提示词

## 开发

```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 测试 API 连接
node test-api.js
```
