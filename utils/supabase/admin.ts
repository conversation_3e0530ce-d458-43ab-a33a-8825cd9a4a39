import { createClient } from './server';

export async function checkIsAdmin(userId: string): Promise<boolean> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('user_profiles')
    .select('role')
    .eq('id', userId)
    .single();

  return data?.role === 'admin';
}

export async function checkUserApprovalStatus(userId: string): Promise<'pending' | 'approved' | 'rejected' | null> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('user_profiles')
    .select('status')
    .eq('id', userId)
    .single();

  return data?.status || null;
}

export async function getRegistrationMode(): Promise<'open' | 'approval_required'> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('app_settings')
    .select('registration_mode')
    .single();

  return data?.registration_mode || 'approval_required';
}