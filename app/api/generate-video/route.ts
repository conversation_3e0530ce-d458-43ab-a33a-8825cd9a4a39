import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import { createClient } from '@/utils/supabase/server';

export const maxDuration = 300; // 5 minutes function timeout

export async function POST(request: Request) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const { prompt } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    const apiKey = process.env.AIHUBMIX_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      );
    }

    console.log('Starting video generation for prompt:', prompt);

    const openai = new OpenAI({
      apiKey: apiKey,
      baseURL: 'https://aihubmix.com/v1',
    });

    const completion = await openai.chat.completions.create({
      model: 'veo-3',
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
      stream: false,
    });

    const responseContent = completion.choices[0].message.content;
    console.log('API Response:', responseContent);
    
    // Parse the response to extract video URLs
    const videoUrlMatch = responseContent?.match(/\[▶️ Watch Online\]\((.*?)\)/);
    const downloadUrlMatch = responseContent?.match(/\[⏬ Download Video\]\((.*?)\)/);
    
    if (!videoUrlMatch || !downloadUrlMatch) {
      console.error('Could not parse video URLs from response');
      return NextResponse.json({
        success: false,
        error: 'Could not parse video URLs from response',
        fullResponse: responseContent,
      });
    }
    
    return NextResponse.json({
      success: true,
      videoUrl: videoUrlMatch[1],
      downloadUrl: downloadUrlMatch[1],
      fullResponse: responseContent,
    });
  } catch (error) {
    console.error('Error generating video:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to generate video';
    const errorDetails = error instanceof Error ? error.toString() : String(error);
    
    if (error instanceof Error && 'code' in error && error.code === 'insufficient_quota') {
      return NextResponse.json(
        { error: '账户余额不足，请充值后再试' },
        { status: 402 }
      );
    }
    
    if (error instanceof Error && 'status' in error && error.status === 401) {
      return NextResponse.json(
        { error: 'Invalid API key' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { 
        error: errorMessage,
        details: errorDetails
      },
      { status: 500 }
    );
  }
}