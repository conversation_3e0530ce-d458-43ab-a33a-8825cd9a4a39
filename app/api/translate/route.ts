import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized - Please login' },
        { status: 401 }
      );
    }

    const { text } = await request.json();

    if (!text) {
      return NextResponse.json({ error: '请提供要翻译的文本' }, { status: 400 });
    }

    // Check if text contains only English characters (basic ASCII letters, numbers, spaces, and common punctuation)
    const englishOnlyRegex = /^[a-zA-Z0-9\s.,!?'"()-]+$/;
    if (englishOnlyRegex.test(text)) {
      // Text is already in English, return as is
      return NextResponse.json({ 
        translatedText: text,
        isTranslated: false,
        originalText: text 
      });
    }

    // Using Google Translate API (free tier) with auto-detect source language
    const response = await fetch(
      `https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=en&dt=t&q=${encodeURIComponent(text)}`
    );

    if (!response.ok) {
      throw new Error('Translation API failed');
    }

    const data = await response.json();
    
    // Extract translated text from Google Translate response
    const translatedText = data[0]
      .map((item: unknown[]) => item[0])
      .filter(Boolean)
      .join('');

    return NextResponse.json({ 
      translatedText,
      isTranslated: true,
      originalText: text
    });

  } catch (error) {
    console.error('Translation error:', error);
    
    // Fallback: return error message
    return NextResponse.json({ 
      error: '翻译失败'
    }, { status: 500 });
  }
}