'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import VideoGenerator from './components/VideoGenerator';
import VideoGallery from './components/VideoGallery';
import VideoResult from './components/VideoResult';
import VideoResultMobile from './components/VideoResultMobile';

interface VideoGeneratorResult {
  prompt: string;
  videoUrl: string;
  downloadUrl: string;
  timestamp: Date;
}
import QuotaInfo from './components/QuotaInfo';
import UserMenu from './components/UserMenu';
import { createClient } from '@/utils/supabase/client';

interface Video {
  id: string;
  prompt: string;
  videoUrl: string;
  downloadUrl: string;
  createdAt: Date;
}

export default function Home() {
  const [videos, setVideos] = useState<Video[]>([]);
  const [latestVideo, setLatestVideo] = useState<Video | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        router.push('/auth');
        return;
      }

      // Check user approval status
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('status')
        .eq('id', user.id)
        .single();

      if (profile?.status === 'pending') {
        await supabase.auth.signOut();
        router.push('/auth?message=pending_approval');
        return;
      } else if (profile?.status === 'rejected') {
        await supabase.auth.signOut();
        router.push('/auth?message=account_rejected');
        return;
      }
      
      setLoading(false);
    };

    checkAuth();

    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_OUT' || !session) {
        router.push('/auth');
      }
    });

    return () => subscription.unsubscribe();
  }, [router, supabase]);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleVideoGenerated = (result: VideoGeneratorResult) => {
    const video: Video = {
      id: Date.now().toString(),
      prompt: result.prompt,
      videoUrl: result.videoUrl,
      downloadUrl: result.downloadUrl,
      createdAt: result.timestamp
    };
    setVideos([video, ...videos]);
    setLatestVideo(video);
  };

  if (loading) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-white text-lg">加载中...</div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <UserMenu />
      <div className="container mx-auto px-4 py-12">
        <header className="text-center mb-12">
          <h1 className="text-5xl font-bold text-white mb-4">
            Veo 3.0 视频生成器
          </h1>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            使用 Google 最新的 Veo 3.0 模型，将您的文字描述转换为高质量视频
          </p>
        </header>

        <VideoGenerator onVideoGenerated={handleVideoGenerated} />
        
        <QuotaInfo />
        
        {videos.length > 0 && (
          <VideoGallery videos={videos} />
        )}
        
        {latestVideo && (
          isMobile ? (
            <VideoResultMobile
              video={latestVideo}
              onClose={() => setLatestVideo(null)}
            />
          ) : (
            <VideoResult
              video={latestVideo}
              onClose={() => setLatestVideo(null)}
            />
          )
        )}
      </div>
    </main>
  );
}
