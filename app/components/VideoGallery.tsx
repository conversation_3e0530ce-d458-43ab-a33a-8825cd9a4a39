'use client';

interface Video {
  id: string;
  prompt: string;
  videoUrl: string;
  downloadUrl: string;
  createdAt: Date;
}

interface VideoGalleryProps {
  videos: Video[];
}

export default function VideoGallery({ videos }: VideoGalleryProps) {
  return (
    <div className="mt-12">
      <h2 className="text-2xl font-bold text-white mb-6 text-center">
        生成的视频
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {videos.map((video) => (
          <div
            key={video.id}
            className="bg-gray-800/50 backdrop-blur-sm rounded-lg overflow-hidden shadow-xl hover:shadow-2xl transition-shadow duration-300"
          >
            <div className="aspect-video bg-gray-900 relative group">
              <video
                src={video.videoUrl}
                controls
                className="absolute inset-0 w-full h-full object-contain"
                poster=""
              >
                您的浏览器不支持视频播放
              </video>
              
              <a
                href={video.downloadUrl}
                download
                className="absolute top-2 right-2 bg-gray-900/80 text-white p-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-gray-800"
                title="下载视频"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
              </a>
            </div>
            
            <div className="p-4">
              <p className="text-gray-300 text-sm line-clamp-2 mb-2">
                {video.prompt}
              </p>
              <p className="text-gray-500 text-xs">
                {new Date(video.createdAt).toLocaleString('zh-CN')}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}