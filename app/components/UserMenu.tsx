'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import type { User } from '@supabase/supabase-js';

export default function UserMenu() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const checkUserAndRole = async (user: User | null) => {
      setUser(user);
      
      if (user) {
        console.log('Checking user role for:', user.email);
        const { data: profile, error } = await supabase
          .from('user_profiles')
          .select('role')
          .eq('id', user.id)
          .single();
        
        if (error) {
          console.error('Error fetching user profile:', error);
        } else {
          console.log('User profile:', profile);
          setIsAdmin(profile?.role === 'admin');
        }
      } else {
        setIsAdmin(false);
      }
      
      setLoading(false);
    };

    // Initial check
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      await checkUserAndRole(user);
    };

    getUser();

    // Force refresh after a short delay to ensure database is synced
    setTimeout(() => {
      getUser();
    }, 1000);

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (_event, session) => {
      await checkUserAndRole(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, [supabase]);

  const handleLogout = async () => {
    await supabase.auth.signOut();
    router.push('/auth');
    router.refresh();
  };

  if (loading) {
    return null;
  }

  if (!user) {
    return null;
  }

  return (
    <div className="absolute top-4 right-4 flex items-center gap-4">
      <div className="text-sm text-gray-300">
        {user.email}
        {isAdmin && (
          <span className="ml-2 px-2 py-1 bg-purple-600 text-white text-xs rounded">
            管理员
          </span>
        )}
      </div>
      {isAdmin && (
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors"
        >
          管理控制台
        </button>
      )}
      <button
        onClick={handleLogout}
        className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
      >
        退出登录
      </button>
    </div>
  );
}