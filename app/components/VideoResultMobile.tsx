'use client';

import { useState } from 'react';

interface VideoResultMobileProps {
  video: {
    id: string;
    prompt: string;
    videoUrl: string;
    downloadUrl: string;
    createdAt: Date;
  };
  onClose?: () => void;
}

export default function VideoResultMobile({ video, onClose }: VideoResultMobileProps) {
  const [showInfo, setShowInfo] = useState(false);

  const handleDownload = async () => {
    try {
      const response = await fetch(video.downloadUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `veo3-video-${video.id}.mp4`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col">
      <div className="flex-shrink-0 bg-gray-900 p-4 flex items-center justify-between">
        <h3 className="text-white font-medium truncate flex-1 mr-4">
          视频生成成功
        </h3>
        {onClose && (
          <button
            onClick={onClose}
            className="text-white/70 hover:text-white transition-colors p-2"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      <div className="flex-1 flex items-center justify-center bg-black">
        <video
          src={video.videoUrl}
          controls
          className="w-full max-h-full object-contain"
          autoPlay
          playsInline
        />
      </div>

      <div className="flex-shrink-0 bg-gray-900">
        <div 
          className="p-4 border-t border-gray-800"
          onClick={() => setShowInfo(!showInfo)}
        >
          <div className="flex items-center justify-between">
            <p className="text-gray-300 text-sm truncate flex-1 mr-4">
              {video.prompt}
            </p>
            <svg 
              className={`w-5 h-5 text-gray-400 transition-transform ${showInfo ? 'rotate-180' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>

        {showInfo && (
          <div className="px-4 pb-4 space-y-3 border-t border-gray-800">
            <div className="text-xs text-gray-400">
              <p>生成时间：{new Date(video.createdAt).toLocaleString('zh-CN')}</p>
              <p>视频ID：{video.id}</p>
            </div>
          </div>
        )}

        <div className="p-4 border-t border-gray-800">
          <button
            onClick={handleDownload}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-medium flex items-center justify-center gap-2 hover:from-blue-700 hover:to-purple-700 transition-all"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            下载视频
          </button>
        </div>
      </div>
    </div>
  );
}