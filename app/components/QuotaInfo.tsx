export default function QuotaInfo() {
  return (
    <div className="max-w-3xl mx-auto mt-8 bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
      <h3 className="text-blue-400 font-semibold text-lg mb-3">
        📊 使用说明
      </h3>
      <div className="text-gray-300 space-y-3 text-sm">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="text-gray-100 font-medium mb-2">注意事项</h4>
            <ul className="space-y-1 text-gray-400">
              <li>• 使用逆向接口，非官方API</li>
              <li>• 不保证稳定性</li>
              <li>• 建议用于开发测试</li>
              <li>• 生成的视频请及时下载</li>
            </ul>
          </div>
          <div>
            <h4 className="text-gray-100 font-medium mb-2">费用说明</h4>
            <ul className="space-y-1 text-gray-400">
              <li>• 单次生成费用：<span className="text-green-400">$0.41</span></li>
              <li>• 相比官方更优惠</li>
              <li>• 需要 AiHubMix 账户</li>
              <li>• 充值后即可使用</li>
            </ul>
          </div>
        </div>
        
        <div className="pt-3 border-t border-gray-700">
          <p className="text-gray-400">
            💡 提示：支持任何语言输入，非英文内容将自动翻译。生成时间通常为1-2分钟，具体取决于服务器负载。
          </p>
        </div>
      </div>
    </div>
  );
}