'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import Link from 'next/link';

interface DebugInfo {
  currentUser?: {
    email?: string;
    id?: string;
    error?: string;
  };
  ownProfile?: {
    data?: {
      id: string;
      email: string;
      role: string;
      credits: number;
      created_at: string;
      updated_at: string;
    };
    error?: string;
  };
  allProfiles?: {
    count: number;
    error?: string;
  };
  appSettings?: {
    data?: Record<string, unknown>;
    error?: string;
  };
  generalError?: unknown;
}

export default function DebugRLS() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({});
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    const runDebugChecks = async () => {
      const results: DebugInfo = {};

      try {
        // 1. Check current user
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        results.currentUser = {
          email: user?.email,
          id: user?.id,
          error: userError?.message
        };

        if (user) {
          // 2. Try to read own profile
          const { data: ownProfile, error: ownError } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', user.id)
            .single();

          results.ownProfile = {
            data: ownProfile,
            error: ownError?.message
          };

          // 3. Try to read all profiles (admin check)
          const { data: allProfiles, error: allError } = await supabase
            .from('user_profiles')
            .select('*');

          results.allProfiles = {
            count: allProfiles?.length || 0,
            error: allError?.message
          };

          // 4. Check app settings
          const { data: settings, error: settingsError } = await supabase
            .from('app_settings')
            .select('*')
            .single();

          results.appSettings = {
            data: settings,
            error: settingsError?.message
          };
        }
      } catch (error) {
        results.generalError = error;
      }

      setDebugInfo(results);
      setLoading(false);
    };

    runDebugChecks();
  }, [supabase]);

  if (loading) {
    return <div className="p-4">Loading debug info...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <h1 className="text-2xl font-bold mb-6">RLS Debug Information</h1>
      <pre className="bg-gray-800 p-4 rounded overflow-auto">
        {JSON.stringify(debugInfo, null, 2)}
      </pre>
      
      <div className="mt-6">
        <h2 className="text-xl font-semibold mb-3">Summary:</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>Current User: {debugInfo.currentUser?.email || 'Not logged in'}</li>
          <li>Can read own profile: {debugInfo.ownProfile?.data ? '✅ Yes' : '❌ No'}</li>
          <li>User role: {debugInfo.ownProfile?.data?.role || 'Unknown'}</li>
          <li>Can read all profiles: {debugInfo.allProfiles?.error ? '❌ No' : '✅ Yes'}</li>
          <li>Total profiles visible: {debugInfo.allProfiles?.count || 0}</li>
        </ul>
      </div>

      <div className="mt-6">
        <Link href="/" className="text-blue-400 hover:underline">← Back to Home</Link>
      </div>
    </div>
  );
}