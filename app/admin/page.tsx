'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';

interface UserProfile {
  id: string;
  email: string;
  role: 'user' | 'admin';
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
}

interface AppSettings {
  registration_mode: 'open' | 'approval_required';
}

export default function AdminDashboard() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  const checkAdminAndLoadData = useCallback(async () => {
    try {
      // Check if user is admin
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      console.log('Auth check:', { user: user?.email, authError });
      
      if (!user) {
        console.log('No user found, redirecting to auth');
        router.push('/auth');
        return;
      }

      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      console.log('Profile check:', { profile, profileError });

      if (profileError) {
        console.error('Error fetching profile:', profileError);
        router.push('/');
        return;
      }

      if (profile?.role !== 'admin') {
        console.log('User is not admin, redirecting to home');
        router.push('/');
        return;
      }

      setIsAdmin(true);

      // Load users
      const { data: usersData } = await supabase
        .from('user_profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (usersData) {
        setUsers(usersData);
      }

      // Load settings
      const { data: settingsData } = await supabase
        .from('app_settings')
        .select('*')
        .single();

      if (settingsData) {
        setSettings(settingsData);
      }
    } catch (error) {
      console.error('Error loading admin data:', error);
    } finally {
      setLoading(false);
    }
  }, [router, supabase]);

  useEffect(() => {
    checkAdminAndLoadData();
  }, [checkAdminAndLoadData]);

  const updateUserStatus = async (userId: string, newStatus: 'approved' | 'rejected') => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      const { error } = await supabase
        .from('user_profiles')
        .update({ 
          status: newStatus,
          approved_at: newStatus === 'approved' ? new Date().toISOString() : null,
          approved_by: newStatus === 'approved' ? user?.id : null
        })
        .eq('id', userId);

      if (!error) {
        setUsers(users.map(u => 
          u.id === userId ? { ...u, status: newStatus } : u
        ));
      }
    } catch (error) {
      console.error('Error updating user status:', error);
    }
  };

  const updateUserRole = async (userId: string, newRole: 'user' | 'admin') => {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .update({ role: newRole })
        .eq('id', userId);

      if (!error) {
        setUsers(users.map(u => 
          u.id === userId ? { ...u, role: newRole } : u
        ));
      }
    } catch (error) {
      console.error('Error updating user role:', error);
    }
  };

  const updateRegistrationMode = async (mode: 'open' | 'approval_required') => {
    try {
      const { error } = await supabase
        .from('app_settings')
        .update({ registration_mode: mode })
        .eq('id', 1);

      if (!error) {
        setSettings({ ...settings!, registration_mode: mode });
      }
    } catch (error) {
      console.error('Error updating registration mode:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-white text-lg">加载中...</div>
      </div>
    );
  }

  if (!isAdmin) {
    return null;
  }

  const pendingUsers = users.filter(u => u.status === 'pending');
  const approvedUsers = users.filter(u => u.status === 'approved');
  const rejectedUsers = users.filter(u => u.status === 'rejected');

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-white">管理员控制台</h1>
          <button
            onClick={() => router.push('/')}
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            返回主页
          </button>
        </div>

        {/* Settings Section */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">系统设置</h2>
          <div className="flex items-center gap-4">
            <span className="text-gray-300">注册模式：</span>
            <select
              value={settings?.registration_mode || 'approval_required'}
              onChange={(e) => updateRegistrationMode(e.target.value as 'open' | 'approval_required')}
              className="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="open">开放注册（自动批准）</option>
              <option value="approval_required">需要审批</option>
            </select>
          </div>
        </div>

        {/* Pending Users */}
        {pendingUsers.length > 0 && (
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-6">
            <h2 className="text-xl font-semibold text-white mb-4">
              待审批用户 ({pendingUsers.length})
            </h2>
            <div className="space-y-3">
              {pendingUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                  <div>
                    <p className="text-white font-medium">{user.email}</p>
                    <p className="text-gray-400 text-sm">
                      申请时间：{new Date(user.created_at).toLocaleString('zh-CN')}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => updateUserStatus(user.id, 'approved')}
                      className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-colors"
                    >
                      批准
                    </button>
                    <button
                      onClick={() => updateUserStatus(user.id, 'rejected')}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors"
                    >
                      拒绝
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Approved Users */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-6">
          <h2 className="text-xl font-semibold text-white mb-4">
            已批准用户 ({approvedUsers.length})
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-gray-400 border-b border-gray-700">
                  <th className="pb-3">邮箱</th>
                  <th className="pb-3">角色</th>
                  <th className="pb-3">注册时间</th>
                  <th className="pb-3">操作</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {approvedUsers.map((user) => (
                  <tr key={user.id} className="border-b border-gray-700/50">
                    <td className="py-3">{user.email}</td>
                    <td className="py-3">
                      <select
                        value={user.role}
                        onChange={(e) => updateUserRole(user.id, e.target.value as 'user' | 'admin')}
                        className="px-3 py-1 bg-gray-700 border border-gray-600 rounded text-sm"
                        disabled={user.email === '<EMAIL>'}
                      >
                        <option value="user">用户</option>
                        <option value="admin">管理员</option>
                      </select>
                    </td>
                    <td className="py-3">{new Date(user.created_at).toLocaleString('zh-CN')}</td>
                    <td className="py-3">
                      {user.email !== '<EMAIL>' && (
                        <button
                          onClick={() => updateUserStatus(user.id, 'rejected')}
                          className="px-3 py-1 bg-red-600/20 hover:bg-red-600/30 text-red-400 rounded text-sm transition-colors"
                        >
                          禁用
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Rejected Users */}
        {rejectedUsers.length > 0 && (
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6">
            <h2 className="text-xl font-semibold text-white mb-4">
              已拒绝用户 ({rejectedUsers.length})
            </h2>
            <div className="space-y-3">
              {rejectedUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                  <div>
                    <p className="text-white font-medium">{user.email}</p>
                    <p className="text-gray-400 text-sm">
                      拒绝时间：{new Date(user.updated_at).toLocaleString('zh-CN')}
                    </p>
                  </div>
                  <button
                    onClick={() => updateUserStatus(user.id, 'approved')}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-colors"
                  >
                    重新批准
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}