# Admin Access Fix Guide

## Problem
The admin user with role 'admin' in the database cannot access the admin panel due to Row Level Security (RLS) policies creating a circular dependency.

## Root Cause
The RLS policy "<PERSON><PERSON> can read all profiles" tries to check if a user is an admin by querying the same `user_profiles` table it's protecting. This creates a circular dependency where:
1. User tries to access admin panel
2. Admin panel queries user's profile to check if they're admin
3. RLS policy needs to verify user is admin to allow the query
4. To verify admin status, it needs to query the user's profile (back to step 2)

## Solutions

### Solution 1: Apply the Migration (Recommended)
1. Run the new migration file in your Supabase dashboard:
   ```sql
   -- Copy contents from: supabase/migrations/003_fix_admin_rls_policies.sql
   ```

2. This migration:
   - Drops the problematic circular policies
   - Creates a `SECURITY DEFINER` function that can check admin status
   - Updates policies to use this function instead of circular queries

### Solution 2: Temporary Quick Fix
If you can't run migrations immediately, you can temporarily disable RLS (NOT recommended for production):

1. Go to Supabase Dashboard > Table Editor > user_profiles
2. Click on "RLS disabled/enabled" toggle to temporarily disable it
3. Test the admin panel
4. **Important**: Re-enable RLS after testing

### Solution 3: Debug the Issue
1. Visit `/admin/debug-rls` to see detailed information about:
   - Current user authentication
   - Profile access permissions
   - What data is visible with current RLS policies

### Solution 4: Manual Database Fix
If the user exists but can't be read due to RLS:

1. In Supabase SQL Editor, run as admin:
   ```sql
   -- Check if the user profile exists
   SELECT * FROM auth.users WHERE email = '<EMAIL>';
   
   -- Get the user ID from above query, then check profile
   SELECT * FROM public.user_profiles WHERE email = '<EMAIL>';
   
   -- If profile is missing, create it manually
   INSERT INTO public.user_profiles (id, email, role, status)
   VALUES (
     'USER_ID_FROM_AUTH_USERS',
     '<EMAIL>',
     'admin',
     'approved'
   )
   ON CONFLICT (id) 
   DO UPDATE SET role = 'admin', status = 'approved';
   ```

## Testing After Fix

1. Clear your browser cache/cookies
2. Log out and log back in
3. Check browser console for debug messages
4. The admin button should appear in UserMenu
5. Clicking it should take you to `/admin` without redirecting

## Additional Debugging

The code now includes console.log statements in:
- `/app/admin/page.tsx` - Shows auth and profile check results
- `/app/components/UserMenu.tsx` - Shows role detection

Check browser console for these messages to understand what's happening.

## Prevention

For future projects:
1. Avoid circular dependencies in RLS policies
2. Use `SECURITY DEFINER` functions for complex permission checks
3. Test RLS policies thoroughly with different user roles
4. Consider using Supabase's built-in auth.jwt() claims for role checks